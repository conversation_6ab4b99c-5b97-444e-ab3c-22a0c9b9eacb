<?php
// 检查用户是否已登录
if (!isset($_SESSION['admin_user_id'])) {
    header('Location: login.php');
    exit;
}

// 获取当前选中的标签页
$current_tab = $_GET['tab'] ?? 'ai_settings';
?>

<div class="ai-service-container">
    <!-- 页面标题 -->
    <div class="page-header">
        <h1><i class="fas fa-robot"></i> AI客服设置</h1>
        <p>配置DeepSeek AI智能客服功能</p>
    </div>

    <!-- 顶部导航标签 -->
    <div class="ai-service-nav">
        <div class="nav-tabs">
            <button class="nav-tab <?php echo $current_tab === 'ai_settings' ? 'active' : ''; ?>" 
                    onclick="switchTab('ai_settings')" data-tab="ai_settings">
                <i class="fas fa-brain"></i>
                <span>DeepSeek</span>
            </button>
            <button class="nav-tab <?php echo $current_tab === 'knowledge_base' ? 'active' : ''; ?>" 
                    onclick="switchTab('knowledge_base')" data-tab="knowledge_base">
                <i class="fas fa-database"></i>
                <span>知识库</span>
            </button>
            <button class="nav-tab <?php echo $current_tab === 'conversation' ? 'active' : ''; ?>" 
                    onclick="switchTab('conversation')" data-tab="conversation">
                <i class="fas fa-comments"></i>
                <span>会话管理</span>
            </button>
        </div>
    </div>

    <!-- 主内容区域 -->
    <div class="ai-service-content">
        <!-- AI设置模块 -->
        <div id="ai_settings-content" class="tab-content <?php echo $current_tab === 'ai_settings' ? 'active' : ''; ?>">
            <div class="settings-card">
                <div class="card-header">
                    <h3><i class="fas fa-cog"></i> AI智能回复设置</h3>
                </div>
                <div class="card-body">
                    <!-- AI开关 -->
                    <div class="form-group">
                        <div class="ai-toggle-container">
                            <label class="switch">
                                <input type="checkbox" id="aiToggle">
                                <span class="slider"></span>
                            </label>
                            <span class="toggle-label">启用AI回复</span>
                        </div>
                    </div>

                    <!-- API密钥管理 -->
                    <div class="form-group">
                        <label class="form-label">API密钥：</label>
                        <div class="api-keys-container">
                            <div class="api-key-input-row">
                                <input type="password" class="form-input" id="aiApiKeyInput" placeholder="输入DeepSeek API密钥">
                                <button type="button" class="btn btn-primary" id="saveApiKeyBtn">保存</button>
                            </div>
                            <div class="api-keys-status" id="apiKeysStatus">
                                <!-- API密钥状态显示区域 -->
                            </div>
                        </div>
                    </div>

                    <!-- 模型选择 -->
                    <div class="form-group">
                        <label class="form-label">模型：</label>
                        <select class="form-select" id="aiModelSelect">
                            <option value="deepseek-chat">DeepSeek Chat</option>
                            <option value="deepseek-reasoner">DeepSeek-R1-0528</option>
                        </select>
                    </div>

                    <!-- 深度思考功能 -->
                    <div class="form-group deep-thinking-section">
                        <div class="deep-thinking-toggle">
                            <label class="switch">
                                <input type="checkbox" id="deepThinkingToggle">
                                <span class="slider"></span>
                            </label>
                            <span class="toggle-label">深度思考 (R1)</span>
                        </div>
                        <div class="deep-thinking-info">仅对DeepSeek-R1-0528模型有效</div>
                    </div>

                    <!-- 回复延迟 -->
                    <div class="form-group">
                        <label class="form-label">回复延迟：</label>
                        <input type="number" min="0" max="60" class="form-input" id="aiReplyDelayInput" placeholder="秒（0为立即回复）">
                    </div>

                    <!-- 系统提示词 -->
                    <div class="form-group">
                        <label class="form-label">系统提示词：</label>
                        <textarea class="form-textarea" id="aiSystemPrompt" rows="4" placeholder="设置AI的角色和回复风格">你是一名专业的微信小店客服人员，请用自然、友好的语言回答用户问题，回答要口语化且不超过50字。注意：不要提及自己是机器人或AI助手，要表现出真人的服务态度。</textarea>
                    </div>

                    <!-- AI状态显示 -->
                    <div class="form-group">
                        <div class="ai-status-display">
                            <div class="status-indicator" id="aiStatusIndicator"></div>
                            <span class="status-text" id="aiStatusText">AI已禁用</span>
                        </div>
                    </div>

                    <!-- 保存按钮 -->
                    <div class="form-actions">
                        <button type="button" class="btn btn-success" id="saveAiSettingsBtn">
                            <i class="fas fa-save"></i> 保存设置
                        </button>
                        <button type="button" class="btn btn-secondary" id="testAiConnectionBtn">
                            <i class="fas fa-plug"></i> 测试连接
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 知识库模块 -->
        <div id="knowledge_base-content" class="tab-content <?php echo $current_tab === 'knowledge_base' ? 'active' : ''; ?>">
            <div class="settings-card">
                <div class="card-header">
                    <h3><i class="fas fa-database"></i> AI智能体知识库</h3>
                </div>
                <div class="card-body">
                    <p class="info-text">知识库功能开发中...</p>
                </div>
            </div>
        </div>

        <!-- 会话管理模块 -->
        <div id="conversation-content" class="tab-content <?php echo $current_tab === 'conversation' ? 'active' : ''; ?>">
            <div class="settings-card">
                <div class="card-header">
                    <h3><i class="fas fa-comments"></i> 会话转接设置</h3>
                </div>
                <div class="card-body">
                    <p class="info-text">会话管理功能开发中...</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- API密钥管理弹窗 -->
<div class="api-keys-modal" id="apiKeysModal" style="display: none;">
    <div class="api-keys-modal-content">
        <div class="api-keys-modal-header">
            <h3>API密钥管理</h3>
            <button class="modal-close-btn" id="closeApiKeysModal">&times;</button>
        </div>
        <div class="api-keys-modal-body">
            <div class="api-keys-list" id="apiKeysModalList">
                <!-- API密钥列表将在这里显示 -->
            </div>
        </div>
    </div>
</div>

<!-- 通知消息 -->
<div id="notification" class="notification" style="display: none;"></div>

<!-- AI客服设置专用样式 -->
<style>
.ai-service-container {
    padding: 20px;
    max-width: 1400px;
    margin: 0 auto;
}

.page-header {
    margin-bottom: 30px;
    text-align: center;
}

.page-header h1 {
    color: white;
    font-size: 28px;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
}

.page-header p {
    color: rgba(255, 255, 255, 0.8);
    font-size: 16px;
    margin: 0;
}

/* 顶部导航标签 */
.ai-service-nav {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 8px;
    margin-bottom: 30px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.nav-tabs {
    display: flex;
    gap: 4px;
}

.nav-tab {
    flex: 1;
    background: transparent;
    border: none;
    padding: 15px 20px;
    border-radius: 10px;
    color: rgba(255, 255, 255, 0.7);
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    transition: all 0.3s ease;
}

.nav-tab:hover {
    background: rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.9);
}

.nav-tab.active {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.nav-tab i {
    font-size: 16px;
}

/* 主内容区域 */
.ai-service-content {
    min-height: 400px;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* 设置卡片 */
.settings-card {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    overflow: hidden;
    backdrop-filter: blur(10px);
}

.card-header {
    background: rgba(255, 255, 255, 0.1);
    padding: 20px 25px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.card-header h3 {
    color: white;
    margin: 0;
    font-size: 18px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.card-body {
    padding: 25px;
}

/* 表单样式 */
.form-group {
    margin-bottom: 20px;
}

.form-label {
    display: block;
    color: rgba(255, 255, 255, 0.9);
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 8px;
}

.form-input, .form-select, .form-textarea {
    width: 100%;
    padding: 12px 15px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    color: white;
    font-size: 14px;
    transition: all 0.3s ease;
}

.form-input:focus, .form-select:focus, .form-textarea:focus {
    outline: none;
    border-color: #4CAF50;
    background: rgba(255, 255, 255, 0.15);
    box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
}

.form-input::placeholder, .form-textarea::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

.form-textarea {
    resize: vertical;
    min-height: 100px;
}

/* 开关样式 */
.switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.2);
    transition: 0.3s;
    border-radius: 24px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: 0.3s;
    border-radius: 50%;
}

input:checked + .slider {
    background-color: #4CAF50;
}

input:checked + .slider:before {
    transform: translateX(26px);
}

.ai-toggle-container, .deep-thinking-toggle {
    display: flex;
    align-items: center;
    gap: 12px;
}

.toggle-label {
    color: rgba(255, 255, 255, 0.9);
    font-size: 14px;
    font-weight: 500;
}

/* API密钥管理 */
.api-keys-container {
    width: 100%;
}

.api-key-input-row {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
}

.api-key-input-row .form-input {
    flex: 1;
}

.api-keys-status {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    padding: 15px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.api-keys-status-display {
    color: rgba(255, 255, 255, 0.7);
    font-size: 13px;
    cursor: pointer;
    padding: 8px 12px;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.api-keys-status-display:hover {
    background: rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.9);
}

/* 深度思考功能 */
.deep-thinking-section {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    padding: 15px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.deep-thinking-info {
    color: rgba(255, 255, 255, 0.6);
    font-size: 12px;
    margin-top: 8px;
    font-style: italic;
}

/* AI状态显示 */
.ai-status-display {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 12px 15px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: #FA5151;
    transition: all 0.3s ease;
}

.status-indicator.active {
    background-color: #4CAF50;
    box-shadow: 0 0 8px rgba(76, 175, 80, 0.5);
}

.status-text {
    color: rgba(255, 255, 255, 0.8);
    font-size: 14px;
}

/* 按钮样式 */
.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.btn-primary {
    background: #2196F3;
    color: white;
}

.btn-primary:hover {
    background: #1976D2;
    transform: translateY(-1px);
}

.btn-success {
    background: #4CAF50;
    color: white;
}

.btn-success:hover {
    background: #45a049;
    transform: translateY(-1px);
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.2);
    color: white;
}

.form-actions {
    display: flex;
    gap: 15px;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* 信息文本 */
.info-text {
    color: rgba(255, 255, 255, 0.7);
    font-size: 14px;
    text-align: center;
    padding: 40px 20px;
}

/* API密钥弹窗样式 */
.api-keys-modal {
    position: fixed;
    z-index: 10003;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
}

.api-keys-modal-content {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    margin: 5% auto;
    padding: 0;
    border-radius: 15px;
    width: 90%;
    max-width: 600px;
    max-height: 80vh;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.api-keys-modal-header {
    background: rgba(255, 255, 255, 0.1);
    padding: 20px 25px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.api-keys-modal-header h3 {
    color: white;
    margin: 0;
    font-size: 18px;
}

.modal-close-btn {
    background: none;
    border: none;
    color: rgba(255, 255, 255, 0.7);
    font-size: 24px;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.modal-close-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    color: white;
}

.api-keys-modal-body {
    padding: 25px;
    max-height: 60vh;
    overflow-y: auto;
}

.api-key-item {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 10px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.api-key-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.api-key-text {
    color: white;
    font-family: monospace;
    font-size: 14px;
}

.api-key-status {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
}

.api-key-status.success {
    background: rgba(76, 175, 80, 0.2);
    color: #4CAF50;
    border: 1px solid rgba(76, 175, 80, 0.3);
}

.api-key-status.error {
    background: rgba(244, 67, 54, 0.2);
    color: #f44336;
    border: 1px solid rgba(244, 67, 54, 0.3);
}

.api-key-actions {
    display: flex;
    gap: 8px;
}

.btn-sm {
    padding: 6px 12px;
    font-size: 12px;
}

.btn-danger {
    background: #f44336;
    color: white;
}

.btn-danger:hover {
    background: #d32f2f;
}

.empty-api-keys {
    text-align: center;
    color: rgba(255, 255, 255, 0.6);
    padding: 40px 20px;
    font-size: 14px;
}

/* 通知样式 */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 20px;
    border-radius: 8px;
    color: white;
    font-size: 14px;
    z-index: 10000;
    max-width: 300px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    animation: slideIn 0.3s ease-out;
}

.notification.success {
    background: #4CAF50;
}

.notification.error {
    background: #f44336;
}

.notification.info {
    background: #2196F3;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .ai-service-container {
        padding: 15px;
    }

    .nav-tabs {
        flex-direction: column;
        gap: 2px;
    }

    .nav-tab {
        padding: 12px 15px;
    }

    .form-actions {
        flex-direction: column;
    }

    .api-key-input-row {
        flex-direction: column;
    }
}
</style>

<!-- AI客服设置专用JavaScript -->
<script>
// AI设置数据
let aiSettings = {
    enabled: false,
    apiKeys: [],
    apiKeyStatus: [],
    currentApiKeyIndex: 0,
    model: 'deepseek-chat',
    deepThinkingEnabled: false,
    replyDelay: 0,
    systemPrompt: '你是一名专业的微信小店客服人员，请用自然、友好的语言回答用户问题，回答要口语化且不超过50字。注意：不要提及自己是机器人或AI助手，要表现出真人的服务态度。'
};

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeAISettings();
    loadAISettings();
    bindEvents();
});

// 标签切换功能
function switchTab(tabName) {
    // 移除所有活动状态
    document.querySelectorAll('.nav-tab').forEach(tab => {
        tab.classList.remove('active');
    });

    document.querySelectorAll('.tab-content').forEach(content => {
        content.classList.remove('active');
    });

    // 激活当前标签
    document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');
    document.getElementById(`${tabName}-content`).classList.add('active');

    // 更新URL参数
    const url = new URL(window.location);
    url.searchParams.set('tab', tabName);
    window.history.pushState({}, '', url);

    // 触发自定义事件
    window.dispatchEvent(new CustomEvent('tabChanged', { detail: { tab: tabName } }));
}

// 初始化AI设置
function initializeAISettings() {
    const elements = {
        aiToggle: document.getElementById('aiToggle'),
        aiApiKeyInput: document.getElementById('aiApiKeyInput'),
        saveApiKeyBtn: document.getElementById('saveApiKeyBtn'),
        aiModelSelect: document.getElementById('aiModelSelect'),
        deepThinkingToggle: document.getElementById('deepThinkingToggle'),
        aiReplyDelayInput: document.getElementById('aiReplyDelayInput'),
        aiSystemPrompt: document.getElementById('aiSystemPrompt'),
        saveAiSettingsBtn: document.getElementById('saveAiSettingsBtn'),
        testAiConnectionBtn: document.getElementById('testAiConnectionBtn'),
        apiKeysStatus: document.getElementById('apiKeysStatus'),
        aiStatusIndicator: document.getElementById('aiStatusIndicator'),
        aiStatusText: document.getElementById('aiStatusText')
    };

    // 设置初始值
    if (elements.aiSystemPrompt) {
        elements.aiSystemPrompt.value = aiSettings.systemPrompt;
    }

    updateAIStatus();
    renderApiKeysList();
}

// 绑定事件
function bindEvents() {
    const elements = {
        aiToggle: document.getElementById('aiToggle'),
        saveApiKeyBtn: document.getElementById('saveApiKeyBtn'),
        aiModelSelect: document.getElementById('aiModelSelect'),
        deepThinkingToggle: document.getElementById('deepThinkingToggle'),
        aiReplyDelayInput: document.getElementById('aiReplyDelayInput'),
        aiSystemPrompt: document.getElementById('aiSystemPrompt'),
        saveAiSettingsBtn: document.getElementById('saveAiSettingsBtn'),
        testAiConnectionBtn: document.getElementById('testAiConnectionBtn')
    };

    // AI开关事件
    if (elements.aiToggle) {
        elements.aiToggle.addEventListener('change', function() {
            aiSettings.enabled = this.checked;
            updateAIStatus();
            showNotification(`AI回复功能已${aiSettings.enabled ? '启用' : '禁用'}`, 'success');
        });
    }

    // 保存API密钥事件
    if (elements.saveApiKeyBtn) {
        elements.saveApiKeyBtn.addEventListener('click', async function() {
            const newApiKey = document.getElementById('aiApiKeyInput').value.trim();
            if (!newApiKey) {
                showNotification("请输入API密钥", 'error');
                return;
            }

            // 检查是否已存在
            if (aiSettings.apiKeys.includes(newApiKey)) {
                showNotification("该API密钥已存在", 'error');
                return;
            }

            // 禁用保存按钮，显示验证中状态
            const originalText = this.textContent;
            this.disabled = true;
            this.textContent = '验证中...';

            try {
                // 验证API密钥
                showNotification("正在验证API密钥...", 'info');
                const validationResult = await validateApiKey(newApiKey);

                if (validationResult.valid) {
                    // 验证成功，保存密钥
                    aiSettings.apiKeys.push(newApiKey);
                    aiSettings.apiKeyStatus.push(true);

                    // 清空输入框
                    document.getElementById('aiApiKeyInput').value = '';

                    // 重新渲染列表
                    renderApiKeysList();
                    updateAIStatus();

                    showNotification("API密钥验证成功并已保存", 'success');
                } else {
                    // 验证失败
                    showNotification(`API密钥验证失败: ${validationResult.error}`, 'error');
                }
            } catch (error) {
                console.error('API密钥验证过程中发生异常:', error);
                showNotification(`验证过程中发生错误: ${error.message}`, 'error');
            } finally {
                // 恢复保存按钮状态
                this.disabled = false;
                this.textContent = originalText;
            }
        });
    }

    // 模型选择事件
    if (elements.aiModelSelect) {
        elements.aiModelSelect.addEventListener('change', function() {
            aiSettings.model = this.value;
            showNotification(`已切换到模型: ${this.value}`, 'success');
        });
    }

    // 深度思考开关事件
    if (elements.deepThinkingToggle) {
        elements.deepThinkingToggle.addEventListener('change', function() {
            aiSettings.deepThinkingEnabled = this.checked;
            showNotification(`深度思考(R1)功能已${aiSettings.deepThinkingEnabled ? '启用' : '禁用'}`, 'success');
        });
    }

    // 回复延迟事件
    if (elements.aiReplyDelayInput) {
        elements.aiReplyDelayInput.addEventListener('change', function() {
            aiSettings.replyDelay = parseInt(this.value) || 0;
            showNotification(`回复延迟已设置为 ${aiSettings.replyDelay} 秒`, 'success');
        });
    }

    // 系统提示词事件
    if (elements.aiSystemPrompt) {
        elements.aiSystemPrompt.addEventListener('change', function() {
            aiSettings.systemPrompt = this.value;
        });
    }

    // 保存设置按钮事件
    if (elements.saveAiSettingsBtn) {
        elements.saveAiSettingsBtn.addEventListener('click', function() {
            saveAISettings();
            showNotification('AI设置已保存', 'success');
        });
    }

    // 测试连接按钮事件
    if (elements.testAiConnectionBtn) {
        elements.testAiConnectionBtn.addEventListener('click', async function() {
            await testAIConnection();
        });
    }
}

// 验证API密钥
async function validateApiKey(apiKey) {
    if (!apiKey || !apiKey.startsWith('sk-')) {
        return {
            valid: false,
            error: 'API密钥格式不正确，应以sk-开头'
        };
    }

    try {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 10000); // 10秒超时

        const response = await fetch('https://api.deepseek.com/v1/models', {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${apiKey}`,
                'Content-Type': 'application/json'
            },
            signal: controller.signal
        });

        clearTimeout(timeoutId);

        if (response.ok) {
            const data = await response.json();
            return {
                valid: true,
                models: data.data || [],
                message: 'API密钥验证成功'
            };
        } else if (response.status === 401) {
            return {
                valid: false,
                error: 'API密钥无效或已过期'
            };
        } else if (response.status === 429) {
            return {
                valid: false,
                error: 'API请求频率过高，请稍后再试'
            };
        } else {
            const errorText = await response.text();
            return {
                valid: false,
                error: `API验证失败 (${response.status}): ${errorText}`
            };
        }
    } catch (error) {
        console.error('API密钥验证异常:', error);
        if (error.name === 'AbortError') {
            return {
                valid: false,
                error: '验证超时，请检查网络连接'
            };
        } else {
            return {
                valid: false,
                error: `验证过程中发生错误: ${error.message}`
            };
        }
    }
}

// 渲染API密钥列表
function renderApiKeysList() {
    const container = document.getElementById('apiKeysStatus');
    if (!container) return;

    if (aiSettings.apiKeys.length === 0) {
        container.innerHTML = '<div class="api-keys-status-display">暂无保存的API密钥</div>';
        return;
    }

    const availableCount = aiSettings.apiKeyStatus.filter(status => status).length;
    const totalCount = aiSettings.apiKeys.length;

    container.innerHTML = `
        <div class="api-keys-status-display" onclick="openApiKeysModal()">
            已保存 ${totalCount} 个API密钥，其中 ${availableCount} 个可用
            <span style="float: right; color: rgba(255, 255, 255, 0.5);">点击管理 →</span>
        </div>
    `;
}

// 更新AI状态显示
function updateAIStatus() {
    const indicator = document.getElementById('aiStatusIndicator');
    const statusText = document.getElementById('aiStatusText');

    if (!indicator || !statusText) return;

    // 重置状态
    indicator.classList.remove('active');

    if (!aiSettings.enabled) {
        statusText.textContent = 'AI已禁用';
    } else if (aiSettings.apiKeys.length === 0) {
        statusText.textContent = 'API密钥未设置';
    } else {
        statusText.textContent = 'AI已启用';
        indicator.classList.add('active');
    }
}

// 测试AI连接
async function testAIConnection() {
    if (aiSettings.apiKeys.length === 0) {
        showNotification('请先添加API密钥', 'error');
        return;
    }

    const btn = document.getElementById('testAiConnectionBtn');
    const originalText = btn.textContent;
    btn.disabled = true;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 测试中...';

    try {
        const currentApiKey = aiSettings.apiKeys[aiSettings.currentApiKeyIndex] || aiSettings.apiKeys[0];
        const result = await validateApiKey(currentApiKey);

        if (result.valid) {
            showNotification('API连接测试成功', 'success');
        } else {
            showNotification(`API连接测试失败: ${result.error}`, 'error');
        }
    } catch (error) {
        showNotification(`测试过程中发生错误: ${error.message}`, 'error');
    } finally {
        btn.disabled = false;
        btn.innerHTML = originalText;
    }
}

// 打开API密钥管理弹窗
function openApiKeysModal() {
    const modal = document.getElementById('apiKeysModal');
    if (modal) {
        modal.style.display = 'block';
        renderApiKeysModal();
    }
}

// 关闭API密钥管理弹窗
function closeApiKeysModal() {
    const modal = document.getElementById('apiKeysModal');
    if (modal) {
        modal.style.display = 'none';
    }
}

// 渲染弹窗中的API密钥列表
function renderApiKeysModal() {
    const container = document.getElementById('apiKeysModalList');
    if (!container) return;

    if (aiSettings.apiKeys.length === 0) {
        container.innerHTML = '<div class="empty-api-keys">暂无保存的API密钥</div>';
        return;
    }

    container.innerHTML = aiSettings.apiKeys.map((key, index) => {
        const maskedKey = key.substring(0, 8) + '...' + key.substring(key.length - 8);
        const status = aiSettings.apiKeyStatus[index] ? '可用' : '不可用';
        const statusClass = aiSettings.apiKeyStatus[index] ? 'success' : 'error';

        return `
            <div class="api-key-item">
                <div class="api-key-info">
                    <div class="api-key-text">${maskedKey}</div>
                    <div class="api-key-status ${statusClass}">${status}</div>
                </div>
                <div class="api-key-actions">
                    <button type="button" class="btn btn-secondary btn-sm" onclick="validateApiKeyFromModal(${index})">验证</button>
                    <button type="button" class="btn btn-danger btn-sm" onclick="deleteApiKeyFromModal(${index})">删除</button>
                </div>
            </div>
        `;
    }).join('');
}

// 从弹窗删除API密钥
function deleteApiKeyFromModal(index) {
    if (index < 0 || index >= aiSettings.apiKeys.length) {
        showNotification('无效的索引', 'error');
        return;
    }

    const deletedKey = aiSettings.apiKeys[index].substring(0, 8) + '...';

    // 删除密钥和状态
    aiSettings.apiKeys.splice(index, 1);
    aiSettings.apiKeyStatus.splice(index, 1);

    // 调整当前索引
    if (aiSettings.currentApiKeyIndex >= aiSettings.apiKeys.length) {
        aiSettings.currentApiKeyIndex = Math.max(0, aiSettings.apiKeys.length - 1);
    }

    // 重新渲染
    renderApiKeysModal();
    renderApiKeysList();
    updateAIStatus();

    showNotification(`API密钥 ${deletedKey} 已删除`, 'success');
}

// 从弹窗验证API密钥
async function validateApiKeyFromModal(index) {
    if (index < 0 || index >= aiSettings.apiKeys.length) {
        showNotification('无效的索引', 'error');
        return;
    }

    const apiKey = aiSettings.apiKeys[index];
    const maskedKey = apiKey.substring(0, 8) + '...';

    try {
        showNotification(`开始验证API密钥 ${maskedKey}`, 'info');
        const validationResult = await validateApiKey(apiKey);

        if (validationResult.valid) {
            // 验证成功，更新状态
            aiSettings.apiKeyStatus[index] = true;
            showNotification(`API密钥 ${maskedKey} 验证成功`, 'success');
        } else {
            // 验证失败，更新状态
            aiSettings.apiKeyStatus[index] = false;
            showNotification(`API密钥 ${maskedKey} 验证失败: ${validationResult.error}`, 'error');
        }

        // 重新渲染
        renderApiKeysModal();
        renderApiKeysList();
        updateAIStatus();

    } catch (error) {
        console.error('API密钥验证过程中发生异常:', error);
        showNotification(`API密钥 ${maskedKey} 验证异常: ${error.message}`, 'error');

        // 验证异常时标记为不可用
        aiSettings.apiKeyStatus[index] = false;
        renderApiKeysModal();
        renderApiKeysList();
        updateAIStatus();
    }
}

// 加载AI设置
function loadAISettings() {
    // 这里可以从服务器加载设置，目前使用本地存储
    const saved = localStorage.getItem('aiServiceSettings');
    if (saved) {
        try {
            const settings = JSON.parse(saved);
            Object.assign(aiSettings, settings);

            // 更新UI
            document.getElementById('aiToggle').checked = aiSettings.enabled;
            document.getElementById('aiModelSelect').value = aiSettings.model;
            document.getElementById('deepThinkingToggle').checked = aiSettings.deepThinkingEnabled;
            document.getElementById('aiReplyDelayInput').value = aiSettings.replyDelay;
            document.getElementById('aiSystemPrompt').value = aiSettings.systemPrompt;

            renderApiKeysList();
            updateAIStatus();
        } catch (error) {
            console.error('加载AI设置失败:', error);
        }
    }
}

// 保存AI设置
function saveAISettings() {
    // 收集当前设置
    aiSettings.enabled = document.getElementById('aiToggle').checked;
    aiSettings.model = document.getElementById('aiModelSelect').value;
    aiSettings.deepThinkingEnabled = document.getElementById('deepThinkingToggle').checked;
    aiSettings.replyDelay = parseInt(document.getElementById('aiReplyDelayInput').value) || 0;
    aiSettings.systemPrompt = document.getElementById('aiSystemPrompt').value;

    // 保存到本地存储
    localStorage.setItem('aiServiceSettings', JSON.stringify(aiSettings));

    // 这里可以添加保存到服务器的逻辑
}

// 显示通知
function showNotification(message, type = 'info') {
    const notification = document.getElementById('notification');
    if (!notification) return;

    notification.textContent = message;
    notification.className = `notification ${type}`;
    notification.style.display = 'block';

    // 3秒后自动隐藏
    setTimeout(() => {
        notification.style.display = 'none';
    }, 3000);
}

// 初始化弹窗事件
document.addEventListener('DOMContentLoaded', function() {
    const modal = document.getElementById('apiKeysModal');
    const closeBtn = document.getElementById('closeApiKeysModal');

    // 关闭按钮事件
    if (closeBtn) {
        closeBtn.addEventListener('click', closeApiKeysModal);
    }

    // 点击弹窗外部关闭
    if (modal) {
        modal.addEventListener('click', function(e) {
            if (e.target === modal) {
                closeApiKeysModal();
            }
        });
    }
});
</script>
